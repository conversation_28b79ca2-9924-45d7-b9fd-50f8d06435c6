from app.library.redis import Redis
from dataclasses import asdict
from functools import wraps
from typing import Any, Callable
import hashlib
import json
import logging


logger = logging.getLogger(__name__)

redis = Redis.get_instances("caching")


class BaseService:
    def __init__(
        self,
        token: str,
        user_id: int,
        account_id: int,
        app_code: str | None = None,
    ):
        self.token = token
        self.user_id = user_id
        self.account_id = account_id
        self.app_code = app_code
        self.headers = {
            "accept": "application/json",
            "content-type": "application/json",
        }


def cache_response(expire_time: int = 300):
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            if not redis:
                return func(self, *args, **kwargs)

            request = args[1] if len(args) > 1 else None
            skip_cache = getattr(request, "skip_cache", False)

            if skip_cache:
                return func(*args, **kwargs)

            # Convert dataclass to dict for json serialization
            serializable_args = [
                asdict(arg) if hasattr(arg, "__dataclass_fields__") else arg
                for arg in args
            ]
            serializable_kwargs = {
                key: asdict(value) if hasattr(value, "__dataclass_fields__") else value
                for key, value in kwargs.items()
            }

            # Generate unique cache key
            unique_key = f"""{self.__class__.__name__}:{func.__name__}:{hashlib.md5(
                f"{json.dumps(serializable_args)}:{json.dumps(serializable_kwargs)}".encode()
            ).hexdigest()}"""
            logger.info(f"unique_key:: {unique_key}")

            # Try to get from cache
            cached: Any = redis.get(unique_key)
            logger.info(f"cached:: {cached}")

            if cached:
                return json.loads(cached)

            # Get fresh data
            result = func(self, *args, **kwargs)

            # Cache the result
            try:
                cache_data = json.dumps(
                    asdict(result)
                    if hasattr(result, "__dataclass_fields__")
                    else result
                )
                if expire_time:
                    redis.setex(unique_key, expire_time, cache_data)
                else:
                    redis.set(unique_key, (cache_data))
            except Exception as e:
                print(f"Caching failed: {e}")

            return result

        return wrapper

    return decorator
