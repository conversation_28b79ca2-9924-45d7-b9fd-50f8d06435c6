from app.services.base import BaseService, cache_response
from config.common.constant import Constant
from dataclasses import dataclass
from typing import Dict, Any
import requests


@dataclass
class SchemaRequest:
    network_id: int
    data_source_id: int
    skip_cache: bool = False


@dataclass
class SchemaResponse:
    data: Dict[str, Any]


class ReportService(BaseService):
    def __init__(
        self,
        token: str,
        user_id: int,
        account_id: int,
    ):
        app_code = "APP_ANTALYSER"
        base_url = Constant.get("ANTALYSER_API_URL")

        super().__init__(token, user_id, account_id, app_code)
        self.base_url = base_url

    @cache_response(expire_time=15 * 60)
    def get_schema(self, request: SchemaRequest) -> SchemaResponse:
        url = f"{self.base_url}/{request.network_id}/api/report/schema"
        params = {
            "_token": self.token,
            "_user_id": self.user_id,
            "_account_id": self.account_id,
            "_app_code": self.app_code,
        }
        data = {"data_source_id": request.data_source_id}

        response = requests.post(
            url,
            json=data,
            params=params,
            headers=self.headers,
        )
        response.raise_for_status()

        return response.json()
