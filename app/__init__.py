import logging.config
from os import environ

from celery import Celery
from dotenv import load_dotenv
from flask import Flask, jsonify
from flask_cors import CORS
from app.common.logging import ContextLogger

import openai

from .config import config as app_config

celery = Celery(__name__)


def create_app():
    load_dotenv()
    APPLICATION_ENV = get_environment()
    openai.api_key = app_config[APPLICATION_ENV].OPENAI_API_KEY
    logging.config.dictConfig(app_config[APPLICATION_ENV].LOGGING)
    logging.setLoggerClass(ContextLogger)
    app = Flask(app_config[APPLICATION_ENV].APP_NAME)
    app.config.from_object(app_config[APPLICATION_ENV])
    app.url_map.strict_slashes = False

    CORS(app, resources={
        r'/api/*': {
            "origins": '*',
            "methods": ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
            "supports_credentials": True,
        }
    })

    from .insight.apis import insight as insight_blueprint
    from .conversation.routes import conversation as conversation_blueprint
    from .message.routes import message as message_blueprint

    app.register_blueprint(
        insight_blueprint,
        url_prefix='/api/v1/insight'
    )

    app.register_blueprint(
        conversation_blueprint,
        url_prefix='/api/v1/conversation'
    )

    app.register_blueprint(
        message_blueprint,
        url_prefix='/api/v1/message'
    )

    return app


def get_environment():
    return environ.get('APPLICATION_ENV') or 'development'
