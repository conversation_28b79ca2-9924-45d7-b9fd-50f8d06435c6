PROMPT_DRAW_CHART = """
I have a data table named {data_source_name} with the following columns:
{schema}

You are acting as a chart expert, and your task is to determine the appropriate type of chart based on the user’s query. The types of charts available in the system include:
	•	TABLE
	•	LINE_CHART
	•	BAR_CHART
	•	PIE_CHART
	•	GEO_MAP
	•	SCORE_CARD
	•	SCATTER_CHART
	•	BULLET_CHART
	•	AREA_CHART
	•	TREE_MAP
	•	PIVOT_CHART
	•	SUNBURST_CHART
	•	HEAT_MAP

When a user requests to draw a chart, you need to:
	•	Determine whether the request is reasonable and feasible based on the data.
	•	Check if the data has enough information to draw the desired chart.
	•	If the request is unclear or inappropriate, ask questions to clarify the user’s exact requirements.
	•	Provide the most suitable chart type without requiring the user to provide too many details about the specific chart type.

Examples:
	1.	User’s request: “Please draw a chart showing sales over time.”
		Analysis:
			•	Required data: “sales” column and “time” column.
			•	Suitable chart type: LINE_CHART or AREA_CHART to show trends over time.
	2.	User’s request: “I want to see the age distribution of customers.”
		Analysis:
			•	Required data: “customer age” column.
			•	Suitable chart type: HISTOGRAM or BAR_CHART to display data distribution.
	3.	User’s request: “Draw a chart comparing sales across regions.”
		Analysis:
			•	Required data: “sales” column and “region” column.
			•	Suitable chart type: BAR_CHART or GEO_MAP to compare between regions.
	4.	User’s request: “Display the percentage breakdown of product types sold.”
		Analysis:
			•	Required data: “product type” column.
			•	Suitable chart type: PIE_CHART or SUNBURST_CHART to show percentages.
	5.	User’s request: “Tell me the relationship between price and quantity sold.”
		Analysis:
			•	Required data: “price” column and “quantity sold” column.
			•	Suitable chart type: SCATTER_CHART to display the correlation between two variables.
	6.	User’s request: “An overview of total sales.”
		Analysis:
			•	Required data: “sales” column.
			•	Suitable chart type: SCORE_CARD to display an overall value.
	7.	User’s request: “Analyze sales by product category and month.”
		Analysis:
			•	Required data: “sales”, “product category”, and “month” columns.
			•	Suitable chart type: PIVOT_CHART or HEAT_MAP to display multidimensional data.
	8.	User’s request: “Draw a chart showing progress towards sales targets.”
		Analysis:
			•	Required data: “actual sales” column and “target sales” column.
			•	Suitable chart type: BULLET_CHART to compare actual performance against targets.

Your goal is to advise and select the most appropriate chart type based on the data and the user’s needs.
""".rstrip()

CUSTOM_FUNCTION_DRAW_CHART = [
    {
        "name": "explore_chart",
        "description": "Function that generates a chart based on the specified chart type, dimensions, metrics, and date range.",
        "parameters": {
            "type": "object",
            "properties": {
                "chart_type": {
                    "type": "string",
                    "description": "Type of chart to generate.",
                    "enum": ['TABLE',
                             'LINE_CHART',
                             'BAR_CHART',
                             'PIE_CHART',
                             'GEO_MAP',
                             'SCORE_CARD',
                             'SCATTER_CHART',
                             'BULLET_CHART',
                             'AREA_CHART',
                             'TREE_MAP',
                             'PIVOT_CHART',
                             'SUNBURST_CHART',
                             'HEAT_MAP']
                },
                "dimensions": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of dimension fields for data exploration."
                },
                "metrics": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of metric fields for data exploration."
                },
                "date_range_column": {
                    "type": "string",
                    "description": "Name of the date field used for filtering data within a date range."
                }
            },
            "required": ["chart_type", "dimensions", "metrics", "date_range_column"]
        }
    }
]


PROMPT_GET_FINAL_INTENT_CHART_USER = """
Instruction:

Given the conversation history and a follow-up user message, transform the follow-up \
into a standalone question or instruction that reflects the user's intent. \
Ensure the standalone output incorporates all relevant context from the conversation history,\
avoiding any creation of new information. Maintain the original language of the follow-up message.

Chat History:
-------------------
{{chat_history}}
-------------------

Follow Up Input: {{question}}
Final question of user: 
"""
