from app.constants import <PERSON>PEN<PERSON>I_MODEL
from app.core.workflows.analytics.enums import Agent, RenderType
from app.core.workflows.analytics.prompt import (
    SUPERVISOR_PROMPT,
    VISUALIZATION_CUSTOM_FUNCTIONS,
    VISUALIZATION_PROMPT,
)
from app.core.workflows.analytics.utils import create_chart_tag_from_properties
from app.services.report import ReportService, SchemaRequest
from langchain_core.messages import SystemMessage, AIMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, START, END, MessagesState
from langgraph.prebuilt import create_react_agent
from langgraph.types import Command
from typing import get_args, cast, TypedDict, Literal
from typing_extensions import TypedDict
import json


WorkerType = Literal[Agent.GENERAL, Agent.VISUALIZER]

NextNode = Literal[WorkerType, "__end__"]
RouteNode = Literal[WorkerType, "FINISH"]

members = list(get_args(WorkerType))
# options = list(get_args(RouteNode))

llm = ChatOpenAI(model=OPENAI_MODEL, temperature=0)


class Router(TypedDict):
    """Agent to route to next. If no agents needed, route to FINISH."""

    next: RouteNode


general_agent = create_react_agent(
    llm,
    tools=[],
    state_modifier="You are a assistant. You can do anything.",
)

visualizer_agent = create_react_agent(
    llm,
    tools=[],
    state_modifier="You are a visualization agent. Create charts and visual representations of data.",
)


def supervisor_node(state: MessagesState) -> Command[NextNode]:
    """Supervisor node that decides the next agent"""

    message = state["messages"][-1]

    # Go to selected agent
    if (
        message.type == "human"
        and message.name != None
        and message.name != Agent.GENERAL
    ):
        return Command(goto=message.name)

    # Currently, we always end the conversation after agent response
    if message.type == "ai" or message.type == "system":
        return Command(goto=END)

    # Can get intent user before
    response = llm.with_structured_output(Router).invoke(
        [SystemMessage(content=SUPERVISOR_PROMPT["detect_agent"])] + state["messages"]
    )
    goto = response["next"]

    if goto == "FINISH":
        goto = END

    return Command(goto=goto)


def general_node(state: MessagesState) -> Command[Literal["supervisor"]]:
    result = general_agent.invoke(state)
    return Command(
        update={
            "messages": [
                AIMessage(content=result["messages"][-1].content, name=Agent.GENERAL),
            ]
        },
        goto="supervisor",
    )


def visualization_node(state: MessagesState, config) -> Command[Literal["supervisor"]]:
    last_message = state["messages"][-1]
    properties = last_message.additional_kwargs.get("properties", {})
    data_source_id = properties.get("data_source_id")
    data_source_name = properties.get("data_source_name")

    if not data_source_id:
        return Command(
            update={
                "messages": [
                    SystemMessage(
                        content="Please select a data source.",
                        name=Agent.VISUALIZER,
                        additional_kwargs={
                            "properties": {
                                "render_type": RenderType.REQUEST_DATA_SOURCE,
                                "user_selected": False,
                            }
                        },
                    )
                ]
            },
            goto="supervisor",
        )

    configuration = config.get("configurable", {})
    auth = configuration.get("auth", {})
    network_id = configuration.get("network_id")

    reportService = ReportService(
        token=auth["token"],
        user_id=auth["user_id"],
        account_id=auth["account_id"],
    )

    schema_response = reportService.get_schema(
        SchemaRequest(
            network_id=network_id,
            data_source_id=data_source_id,
        )
    )
    schemas = schema_response.get("data", {}).get("schemas", None)

    if not schemas:
        raise ValueError("No schemas found")

    schemas_fmt = "\n".join(
        f"name: {schema.get('label')}, data_type: {schema.get('dataType')}"
        for schema in schemas
    )

    chat_history = "\n".join(
        [
            f"{'User' if i % 2 == 0 else 'Assistant'}: {msg.content}"
            for i, msg in enumerate(state["messages"][:-1])
        ]
    )

    intent_response = llm.invoke(
        [
            {
                "role": "system",
                "content": VISUALIZATION_PROMPT["get_final_intent"].format(
                    chat_history=chat_history,
                    question=state["messages"][-1].content,
                ),
            }
        ]
    )

    function_response = llm.bind_functions(
        functions=[VISUALIZATION_CUSTOM_FUNCTIONS["draw_chart"]],
        function_call="auto",
    ).invoke(
        [
            SystemMessage(
                content=VISUALIZATION_PROMPT["draw_chart"].format(
                    schemas=schemas_fmt,
                    data_source_name=data_source_name,
                ),
            ),
            AIMessage(content=intent_response.content),
        ]
    )
    print(f"function_response:: {function_response.to_json()}")
    finish_reason = function_response.response_metadata.get("finish_reason")

    if finish_reason != "function_call":
        return Command(
            update={
                "messages": [
                    AIMessage(content=function_response.content, name=Agent.VISUALIZER)
                ]
            },
            goto="supervisor",
        )
    else:
        function_call_args = json.loads(
            cast(
                str,
                function_response.additional_kwargs.get("function_call", {}).get(
                    "arguments"
                ),
            )
        )

        properties = {
            "render_type": RenderType.CHART,
            "function_call_args": function_call_args,
            "schemas": schemas,
            "data_source_id": data_source_id,
            "data_source_name": data_source_name,
        }
        content = create_chart_tag_from_properties(properties, True)

        return Command(
            update={
                "messages": [
                    AIMessage(
                        content=content,
                        name=Agent.VISUALIZER,
                        additional_kwargs={"properties": properties},
                    )
                ]
            },
            goto="supervisor",
        )


def create_analytics_workflow():
    """Create and configure the analytics workflow graph"""
    builder = StateGraph(MessagesState)

    builder.add_node("supervisor", supervisor_node)
    builder.add_node("general", general_node)
    builder.add_node("visualizer", visualization_node)
    builder.add_edge(START, "supervisor")

    graph = builder.compile()

    # save graph image
    # save_graph(graph, "app/core/workflows/analytics/analytics_workflow.png")

    return graph
