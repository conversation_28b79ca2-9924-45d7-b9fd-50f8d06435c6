from typing import TypeVar
from app.core.workflows.analytics.types import ChartAttributes


def create_chart_tag(attributes: ChartAttributes) -> str:
    identifier = attributes["identifier"]
    dimension = attributes["dimension"]
    metric = attributes["metric"]
    dateRange = attributes["dateRange"]

    return f'<antsomiview identifier="{identifier}" dimension="{",".join(dimension)}" metric="{",".join(metric)}" dateRange="{dateRange}"></antsomiview>'


def create_chart_tag_from_properties(
    properties: dict,
    for_client: bool = False,
) -> str:
    schemas = properties.get("schemas", [])
    function_call_args = properties.get("function_call_args", {})

    identifier = function_call_args["chart_type"]
    dimension = function_call_args["dimension"]
    metric = function_call_args["metric"]
    dateRange = function_call_args["date_range"] or ""
    # dateRange = ""

    # client need name, server need label for messages
    if for_client:
        dimension = schemas_labels_to_names(dimension, schemas)
        metric = schemas_labels_to_names(metric, schemas)
        dateRange = schemas_labels_to_names(dateRange, schemas)

    return create_chart_tag(
        {
            "identifier": identifier,
            "dimension": dimension,
            "metric": metric,
            "dateRange": dateRange,
        }
    )


T = TypeVar("T", str, list[str])


def schemas_labels_to_names(
    labels: T,
    schemas: dict,
) -> T:
    schemas_map = {schema["label"]: schema for schema in schemas}
    if isinstance(labels, list):
        return [
            schemas_map[label].get("name") if label in schemas_map else ""
            for label in labels
        ]
    return schemas_map[labels].get("name") if labels else ""
