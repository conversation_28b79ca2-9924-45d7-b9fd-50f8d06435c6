SUPERVISOR_PROMPT = {
    "detect_agent": """
You are a supervisor managing these specialized agents:
    
Agents and their responsibilities:

1. Visualizer Agent:
   Purpose: Helps users understand their data through visual representations and analysis
   Handles:
   • Data exploration and understanding requests
   • Business performance analysis
   • Metric comparisons and trends
   • Any request that aims to gain insights from data

2. General Agent:
   Purpose: Manages conversation flow and non-analytical requests
   Handles:
   • General conversation and queries
   • Non-data related assistance
   • Task clarification and guidance

Examples:
User: "Hello, how are you?" → general (simple greeting)
User: "I need to analyze our marketing campaigns" → visualizer (seeks data insights)
User: "What's the weather?" → general (non-business query)
User: "How are our campaigns doing?" → visualizer (seeks performance understanding)
User: "Thanks for your help" → general (conversation closure)

Core Rule: Route to visualizer if the user's intent is to understand or explore their data, regardless of how they phrase it.

Given the user request above, which agent should handle it next?""",
}

VISUALIZATION_PROMPT = {
    "get_final_intent": """
Instruction:
Given the conversation history and a follow-up user message, transform the follow-up into a standalone question or instruction that reflects the user's complete intent. The output should:
- Incorporate all relevant context from the conversation history ONLY for visualization-related requests
- Avoid creating new information
- Maintain the original language of the follow-up message
- Preserve chart-specific context (dimension, metric, chart types)

Important: For the following types of messages, retain the original message without transformation
- General questions (e.g., "Can you suggest me some insights about my data?")
- Greetings (e.g., "hello", "hi")
- Acknowledgments (e.g., "thanks", "thank you")
- Open-ended requests for insights/suggestions
- Casual conversation

Examples:

### Example 1: Metric Change
Chat History:
-------------------
User: Hi! I need to analyze our marketing campaigns.
Assistant: '{{"chart_type":"BAR_CHART","dimension":["campaign_name"],"metric":["roi"],"date_range":"date"}}'
-------------------
Follow Up Input: can you show me the click-through rate instead of ROI?
Final question of user: Show click-through rate by campaign_name in a bar chart

### Example 2: Adding dimension
Chat History:
-------------------
User: Hi! Can we analyze product sales data?
Assistant: '{{"chart_type":"LINE_CHART","dimension":["date"],"metric":["revenue"],"date_range":"sale_date"}}'
-------------------
Follow Up Input: what's the performance by product category?
Final question of user: Show revenue by product category over time in a line chart

### Example 3: Chart Type Switch
Chat History:
-------------------
User: Hi! I need to review email campaign performance.
Assistant: '{{"chart_type":"BAR_CHART","dimension":["campaign_name"],"metric":["open_rate","click_rate"],"date_range":"send_date"}}'
User: can you show campaigns with click rates above 15%?
Assistant: '{{"chart_type":"TABLE","dimension":["campaign_name"],"metric":["click_rate"],"filters":{{"click_rate":">15%"}},"date_range":"send_date"}}'
-------------------
Follow Up Input: switch this to a line chart.
Final question of user: Show campaigns with click rates above 15% in a line chart

### Example 4: Suggestion Response
Chat History:
-------------------
User: what do you suggest we explore next?
Assistant: We could analyze trends in low-performing categories or look at geographic sales distribution. Which one would you prefer?
-------------------
Follow Up Input: Can you suggest me some insights about my data?
Final question of user: Can you suggest me some insights about my data?

### Example 5: New Chart Request After Break
Chat History:
-------------------
User: Hi! Let's analyze our sales data.
Assistant: '{{"chart_type":"LINE_CHART","dimension":["date"],"metric":["revenue"],"date_range":"sale_date"}}'
User: Interesting. Thanks!
Assistant: You're welcome! Let me know if you need any other insights.
-------------------
Follow Up Input: Can you show me customer satisfaction scores?
Final question of user: Show customer satisfaction scores

Your Task:
Given the chat history below and follow-up input, generate a standalone question that captures the user's complete intent:

Chat History:
-------------------
{chat_history}
-------------------
Follow Up Input: {question}
Final question of user:
""",
    "draw_chart": """
I have a data table named {data_source_name} with the following columns:
{schemas}

You are acting as a chart expert, and your task is to determine the appropriate type of chart based on the user's query. The types of charts available in the system include:
	•	TABLE
	•	LINE_CHART
	•	BAR_CHART
	•	PIE_CHART
	•	GEO_MAP
	•	SCORE_CARD
	•	SCATTER_CHART
	•	BULLET_CHART
	•	AREA_CHART
	•	TREE_MAP
	•	PIVOT_CHART
	•	SUNBURST_CHART
	•	HEAT_MAP

When a user requests to draw a chart, you need to:
	•	Determine whether the request is reasonable and feasible based on the data.
	•	Check if the data has enough information to draw the desired chart.
	•	If the request is unclear or inappropriate, ask questions to clarify the user's exact requirements.
	•	Provide the most suitable chart type without requiring the user to provide too many details about the specific chart type.

Examples:
	1.	User's request: "Please draw a chart showing sales over time."
		Analysis:
			•	Required data: "sales" column and "time" column.
			•	Suitable chart type: LINE_CHART or AREA_CHART to show trends over time.
	2.	User's request: "I want to see the age distribution of customers."
		Analysis:
			•	Required data: "customer_age" column.
			•	Suitable chart type: HISTOGRAM or BAR_CHART to display data distribution.
	3.	User's request: "Draw a chart comparing sales across regions."
		Analysis:
			•	Required data: "sales" column and "region" column.
			•	Suitable chart type: BAR_CHART or GEO_MAP to compare between regions.
	4.	User's request: "Display the percentage breakdown of product types sold."
		Analysis:
			•	Required data: "product_type" column.
			•	Suitable chart type: PIE_CHART or SUNBURST_CHART to show percentages.
	5.	User's request: "Tell me the relationship between price and quantity sold."
		Analysis:
			•	Required data: "price" column and "quantity_sold" column.
			•	Suitable chart type: SCATTER_CHART to display the correlation between two variables.
	6.	User's request: "An overview of total sales."
		Analysis:
			•	Required data: "sales" column.
			•	Suitable chart type: SCORE_CARD to display an overall value.
	7.	User's request: "Analyze sales by product category and month."
		Analysis:
			•	Required data: "sales", "product_category", and "month" columns.
			•	Suitable chart type: PIVOT_CHART or HEAT_MAP to display multidimensional data.
	8.	User's request: "Draw a chart showing progress towards sales targets."
		Analysis:
			•	Required data: "actual_sales" column and "target_sales" column.
			•	Suitable chart type: BULLET_CHART to compare actual performance against targets.

Your goal is to advise and select the most appropriate chart type based on the data and the user's needs.
""",
}

VISUALIZATION_CUSTOM_FUNCTIONS = {
    "draw_chart": {
        "name": "draw_chart",
        "description": "Function that generates a chart based on the specified chart type, dimension, metric, and date range.",
        "parameters": {
            "type": "object",
            "properties": {
                "chart_type": {
                    "type": "string",
                    "description": "Type of chart to generate.",
                    "enum": [
                        "TABLE",
                        "LINE_CHART",
                        "BAR_CHART",
                        "PIE_CHART",
                        "GEO_MAP",
                        "SCORE_CARD",
                        "SCATTER_CHART",
                        "BULLET_CHART",
                        "AREA_CHART",
                        "TREE_MAP",
                        "PIVOT_CHART",
                        "SUNBURST_CHART",
                        "HEAT_MAP",
                    ],
                },
                "dimension": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of dimension fields for data exploration.",
                },
                "metric": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of metric fields for data exploration.",
                },
                "date_range": {
                    "type": ["string", "null"],
                    "description": "Name of the date field used for filtering data within a date range. Returns the date field if chart type is LINE_CHART, returns null for other chart types unless date range dimension is specifically requested by user.",
                },
            },
            "required": ["chart_type", "dimension", "metric"],
        },
    }
}
