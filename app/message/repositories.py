from app.common.utils.database import (
    get_listing_result,
    build_where_clause,
    build_insert_clause,
    build_set_clause,
)
from app.common.enums import SortDirection
from app.common.types import QueryResult
from app.core.workflows.analytics.enums import Agent
from app.library.database import DatabasePool
from app.message.enums import Role, MessageColumn
from app.message.types import Message
from datetime import datetime
from psycopg2.extras import RealDictCursor
from psycopg2.sql import SQL, Identifier
from typing import Optional
from uuid import uuid4, UUID
import psycopg2


def get_messages(
    conversation_id: UUID,
    # network_id: int,
    # user_id: int,
    page: int = 1,
    limit: int = 25,
    sort: MessageColumn = MessageColumn.CTIME,
    sort_direction: SortDirection = SortDirection.DESC,
) -> QueryResult[Message]:
    db_pool = DatabasePool.get_instance("info_master_genai")
    try:
        with db_pool.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                with conn:
                    data_binding = {
                        "conversation_id": str(conversation_id),
                        # "network_id": network_id,
                        # "user_id": user_id,
                    }

                    where_clause = build_where_clause(data_binding)

                    query = SQL(
                        """
                        WITH filtered_data AS (
                            SELECT 
                                message_id,
                                conversation_id,
                                role,
                                content,
                                agent,
                                properties,
                                token_count,
                                ctime,
                                utime
                            FROM conversation.messages
                            WHERE {where_clause}
                        )
                        SELECT 
                            fd.*,
                            COUNT(*) OVER() as total_count
                        FROM filtered_data fd
                        ORDER BY {sort} {sort_direction}
                        LIMIT {limit}
                        OFFSET {offset}
                        """
                    ).format(
                        where_clause=SQL(where_clause),
                        sort=Identifier(sort.value),
                        sort_direction=SQL(sort_direction.value),
                        limit=SQL(str(limit)),
                        offset=SQL(str((page - 1) * limit)),
                    )

                    cur.execute(query, data_binding)
                    results = cur.fetchall()
                    return get_listing_result(results, Message)
    except psycopg2.Error as e:
        raise e


def get_message(message_id: UUID) -> Message:
    db_pool = DatabasePool.get_instance("info_master_genai")
    try:
        with db_pool.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                with conn:
                    data_binding = {
                        "message_id": str(message_id),
                    }

                    query = """
                        SELECT 
                            message_id,
                            conversation_id,
                            role,
                            content,
                            agent,
                            properties,
                            token_count,
                            ctime,
                            utime
                        FROM conversation.messages
                        WHERE message_id = %(message_id)s
                    """

                    cur.execute(query, data_binding)
                    result = cur.fetchone()
                    return result
    except psycopg2.Error as e:
        raise e


def create_message(
    conversation_id: UUID,
    role: Role,
    content: str,
    agent: Optional[Agent] = None,
    properties: Optional[dict] = None,
    token_count: Optional[int] = None,
    ctime: Optional[datetime] = None,
) -> Message:
    db_pool = DatabasePool.get_instance("info_master_genai")
    try:
        with db_pool.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                with conn:
                    data_binding = {
                        "message_id": str(uuid4()),
                        "conversation_id": str(conversation_id),
                        "role": role,
                        "content": content,
                        "agent": agent,
                        "properties": properties,
                        "token_count": token_count,
                        "ctime": ctime,
                        "utime": ctime,
                    }

                    insert_clause = build_insert_clause(data_binding)

                    query = SQL(
                        """
                        INSERT INTO conversation.messages 
                        {insert_clause}
                        RETURNING *
                        """
                    ).format(insert_clause=SQL(insert_clause))

                    cur.execute(query, data_binding)
                    result = cur.fetchone()
                    conn.commit()
                    return result
    except psycopg2.Error as e:
        raise e


def update_message(
    message_id: UUID,
    content: Optional[str] = None,
    properties: Optional[dict] = None,
) -> Message:
    db_pool = DatabasePool.get_instance("info_master_genai")
    try:
        with db_pool.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                with conn:
                    where_binding = {
                        "message_id": str(message_id),
                    }
                    set_binding = {
                        "content": content,
                        "properties": properties,
                    }

                    where_clause = build_where_clause(where_binding)

                    set_clause = (
                        build_set_clause(set_binding) + ", utime = CURRENT_TIMESTAMP"
                    )

                    query = SQL(
                        """
                        UPDATE conversation.messages
                        SET {set_clause}
                        WHERE {where_clause}
                        RETURNING *
                        """
                    ).format(
                        set_clause=SQL(set_clause),
                        where_clause=SQL(where_clause),
                    )

                    cur.execute(query, {**set_binding, **where_binding})
                    result = cur.fetchone()
                    conn.commit()
                    return result
    except psycopg2.Error as e:
        raise e
