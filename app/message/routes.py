from app.common.decorators.error_handlers import handle_errors
from app.message import business
from app.message.schemas import (
    ConversationIdParams,
    CreateMessageRequest,
    MessageIdParams,
    UpdateMessageRequest,
)
from app.common.responses import JSONResponse
from app.common.schemas import BaseQueryParams
from authentication import check_permission
from flask import Blueprint, current_app, request
from uuid import UUID
from werkzeug.local import LocalProxy

logger = LocalProxy(lambda: current_app.logger)

message = Blueprint("message", __name__)


@message.before_request
def before_request_func():
    current_app.logger.name = "message"


# @message.route("/<conversation_id>", methods=["GET"])
# @check_permission
# @handle_errors
# def get_messages(conversation_id: UUID):
#     param = ConversationIdParams(conversation_id=conversation_id)
#     query = GetMessagesQueryParams(**dict(request.args))  # type: ignore

#     response = business.get_messages(
#         conversation_id=param.conversation_id,
#         network_id=query.network_id,
#         user_id=query.user_id,
#         page=query.page,
#         limit=query.limit,
#         sort=query.sort,
#         sort_direction=query.sort_direction,
#     )

#     return JSONResponse(
#         code=200,
#         message="Success",
#         data=response,
#     )


@message.route("/<message_id>", methods=["GET"])
@check_permission
@handle_errors
def get_message(message_id: UUID):
    param = MessageIdParams(message_id=message_id)
    query = BaseQueryParams(**dict(request.args))  # type: ignore

    response = business.get_message(
        message_id=param.message_id,
        network_id=query.network_id,
        user_id=query.user_id,
    )

    return JSONResponse(
        code=200,
        message="Success",
        data=response,
    )


@message.route("/<message_id>", methods=["PUT"])
@check_permission
@handle_errors
def update_message(message_id: UUID):
    param = MessageIdParams(message_id=message_id)
    query = BaseQueryParams(**dict(request.args))  # type: ignore
    body = UpdateMessageRequest(**dict(request.json))  # type: ignore

    response = business.update_message(
        message_id=param.message_id,
        network_id=query.network_id,
        user_id=query.user_id,
        content=body.content,
        properties=body.properties,
    )

    return JSONResponse(
        code=200,
        message="Success",
        data=response,
    )


@message.route("/<conversation_id>", methods=["POST"])
@check_permission
@handle_errors
def completion(conversation_id: UUID):
    param = ConversationIdParams(conversation_id=conversation_id)
    query = BaseQueryParams(**dict(request.args))  # type: ignore
    body = CreateMessageRequest(**dict(request.json))  # type: ignore

    response = business.completion(
        conversation_id=param.conversation_id,
        messages=body.messages,
        network_id=query.network_id,
        user_id=query.user_id,
        account_id=query.account_id,
        token=query.token,
    )

    return JSONResponse(
        code=200,
        message="Success",
        data=response,
    )
