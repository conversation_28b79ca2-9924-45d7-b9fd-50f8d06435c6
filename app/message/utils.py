from app.core.workflows.analytics.enums import Agent, RenderType
from app.core.workflows.analytics.utils import create_chart_tag_from_properties
from app.message.types import CompletionMessage
from langchain_core.messages import HumanMessage, AIMessage
from typing import List, cast


def to_langgraph_messages(
    messages: List[CompletionMessage],
) -> list[HumanMessage | AIMessage]:
    return [
        (
            HumanMessage(
                name=msg.get("agent"),
                content=cast(str, msg.get("content", "")),
                additional_kwargs={"properties": msg.get("properties", {})},
            )
            if msg.get("role") == "user"
            else AIMessage(
                name=msg.get("agent"),
                content=cast(str, msg.get("content", "")),
                additional_kwargs={"properties": msg.get("properties", {})},
            )
        )
        for msg in messages[:-1]
    ] + [
        HumanMessage(
            name=messages[-1].get("agent"),
            content=cast(str, messages[-1].get("content", "")),
            additional_kwargs={"properties": messages[-1].get("properties", {})},
        )
    ]


def to_api_messages(messages: List[CompletionMessage]) -> List[CompletionMessage]:
    new_messages = []

    for msg in messages:
        content = msg.get("content", "")

        if not content:
            agent = msg.get("agent")

            if agent == Agent.VISUALIZER:
                msg["content"] = build_content_visualizer(msg)

        new_messages.append(msg)

    return new_messages


def build_content_visualizer(message: CompletionMessage) -> str:
    content = message.get("content") or ""
    properties = message.get("properties") or {}
    render_type = properties.get("render_type")
    data_source_name = properties.get("data_source_name")

    if render_type == RenderType.REQUEST_DATA_SOURCE:
        return "Please select a data source"

    if render_type == RenderType.SELECT_DATA_SOURCE:
        return f"Use data source {data_source_name}"

    if render_type == RenderType.CHART:
        return create_chart_tag_from_properties(properties)

    return content
