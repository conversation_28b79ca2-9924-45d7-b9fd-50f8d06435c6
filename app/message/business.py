from app.core.workflows.analytics.enums import Agent
from app.core.workflows.analytics.workflow import create_analytics_workflow
from app.message import repositories
from app.message.enums import Role
from app.message.types import CompletionMessage, Message, CompletionResponse
from app.message.utils import to_api_messages, to_langgraph_messages
from datetime import datetime
from typing import List, cast, Optional
from uuid import UUID
import json
import logging


logger = logging.getLogger(__name__)


def completion(
    conversation_id: UUID,
    messages: List[CompletionMessage],
    network_id: int,
    user_id: int,
    account_id: int,
    token: str,
) -> CompletionResponse:
    start_time = datetime.now()
    try:
        logger.info(
            f"""params: {json.dumps({
                "conversation_id": str(conversation_id),
                "network_id": network_id,
                "user_id": user_id,
                "account_id": account_id,
                "messages": messages,
            })}""",
        )
        messages = to_api_messages(messages)

        user_message = messages[-1]
        user_content = user_message.get("content", "")
        user_agent = user_message.get("agent", None)
        user_properties = user_message.get("properties", {})

        if not user_content:
            raise Exception(
                f"User message content cannot be empty. Message: {user_content}"
            )

        graph = create_analytics_workflow()
        completion_response = graph.invoke(
            {"messages": to_langgraph_messages(messages)},
            {
                "configurable": {
                    "auth": {
                        "user_id": user_id,
                        "account_id": account_id,
                        "token": token,
                    },
                    "network_id": network_id,
                },
            },
        )
        completion_message = completion_response["messages"][-1]
        logger.info(f"completion_message: {completion_message}")

        completion_agent: Agent = completion_message.name
        completion_content = completion_message.content
        completion_properties = completion_message.additional_kwargs.get(
            "properties", {}
        )

        if not completion_content:
            raise Exception(f"Cannot completion message")

        user_message = repositories.create_message(
            conversation_id=conversation_id,
            role=Role.USER,
            content=user_content,
            agent=user_agent,
            properties=user_properties,
        )
        logger.info(f"create_user_message: {user_message}")

        assistant_message = repositories.create_message(
            conversation_id=conversation_id,
            role=Role.ASSISTANT,
            content=completion_content,
            token_count=None,  # TODO: Currently not saving token_count
            agent=completion_agent,
            properties=completion_properties,
        )
        logger.info(f"create_assistant_message: {assistant_message}")

        return {
            "conversation_id": conversation_id,
            "messages": [
                user_message,
                assistant_message,
            ],
        }
    except Exception as e:
        logger.error(f"error: {str(e)}")
        raise Exception(e)
    finally:
        logger.info(
            f"duration_ms: {(datetime.now() - start_time).total_seconds() * 1000}"
        )


def get_message(
    message_id: UUID,
    network_id: int,
    user_id: int,
) -> Message:
    start_time = datetime.now()
    try:
        logger.info(
            f"""params: {json.dumps({
                'message_id': str(message_id),
                'network_id': network_id,
                'user_id': user_id,
            })}""",
        )

        response = repositories.get_message(message_id=message_id)
        logger.info(f"response: {response}")

        message: Message = cast(
            Message,
            {
                key: value
                for key, value in response.items()
                if key not in ["token_count"]
            },
        )

        return message
    except Exception as e:
        logger.error(f"error: {str(e)}")
        raise Exception(e)
    finally:
        logger.info(
            f"duration_ms: {(datetime.now() - start_time).total_seconds() * 1000}"
        )


def update_message(
    message_id: UUID,
    network_id: int,
    user_id: int,
    content: Optional[str] = None,
    properties: Optional[dict] = None,
) -> Message:
    start_time = datetime.now()
    try:
        logger.info(
            f"""params: {json.dumps({
                'message_id': str(message_id),
                'network_id': network_id,
                'user_id': user_id,
                'content': content,
                'properties': properties,
            })}""",
        )

        response = repositories.update_message(
            message_id=message_id,
            content=content,
            properties=properties,
        )
        logger.info(f"response: {response}")

        message: Message = cast(
            Message,
            {
                key: value
                for key, value in response.items()
                if key not in ["token_count"]
            },
        )

        return message
    except Exception as e:
        logger.error(f"error: {str(e)}")
        raise Exception(e)
    finally:
        logger.info(
            f"duration_ms: {(datetime.now() - start_time).total_seconds() * 1000}"
        )
