from app.common.schemas import GetListQueryParamsBase
from app.message.enums import MessageColumn
from app.message.types import CompletionMessage
from pydantic import BaseModel
from typing import Optional, List
from uuid import UUID


class CreateMessageRequest(BaseModel):
    messages: List[CompletionMessage]


class ConversationIdParams(BaseModel):
    conversation_id: UUID


class MessageIdParams(BaseModel):
    message_id: UUID


class GetMessagesQueryParams(GetListQueryParamsBase[MessageColumn]):
    sort: MessageColumn


class UpdateMessageRequest(BaseModel):
    content: Optional[str] = None
    properties: Optional[dict] = None
