from app.core.workflows.analytics.enums import Agent
from app.message.enums import Role
from datetime import datetime
from typing import Optional, Any, List
from typing_extensions import TypedDict, NotRequired
from uuid import UUID


class Message(TypedDict):
    message_id: str
    conversation_id: str
    role: Role
    content: Optional[str]
    agent: Optional[Agent]
    properties: NotRequired[Optional[dict[str, Any]]]
    token_count: NotRequired[Optional[int]]
    ctime: datetime
    utime: datetime


class CompletionMessage(TypedDict):
    role: Role
    # content: str
    content: NotRequired[Optional[str]]
    agent: NotRequired[Optional[Agent]]
    properties: NotRequired[Optional[dict[str, Any]]]


class CompletionResponse(TypedDict):
    conversation_id: UUID
    messages: List[Message]
