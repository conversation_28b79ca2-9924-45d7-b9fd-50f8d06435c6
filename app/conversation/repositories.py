from app.common.enums import Status, SortDirection
from app.common.types import QueryResult
from app.common.utils.database import (
    build_insert_clause,
    build_set_clause,
    build_where_clause,
    get_listing_result,
)
from app.conversation.enums import ConversationColumn
from app.conversation.types import Conversation, ConversationWithMessages
from app.library.database import <PERSON><PERSON><PERSON>
from datetime import datetime
from psycopg2.extras import RealDictCursor
from psycopg2.sql import SQL, Identifier
from typing import Optional, List
from uuid import uuid4, UUID
import psycopg2


def create_conversation(
    network_id: int,
    user_id: int,
    title: str,
    model_version: str,
    status: Status = Status.ACTIVE,
    ctime: Optional[datetime] = None,
) -> Conversation:
    db_pool = DatabasePool.get_instance("info_master_genai")
    try:
        with db_pool.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                with conn:
                    data_binding = {
                        "conversation_id": str(uuid4()),
                        "network_id": network_id,
                        "user_id": user_id,
                        "title": title,
                        "model_version": model_version,
                        "status": status,
                        "ctime": ctime,
                        "utime": ctime,
                    }

                    insert_clause = build_insert_clause(data_binding)

                    query = SQL(
                        """
                        INSERT INTO conversation.conversations 
                        {insert_clause}
                        RETURNING *
                        """
                    ).format(insert_clause=SQL(insert_clause))

                    cur.execute(query, data_binding)
                    result = cur.fetchone()
                    return result
    except psycopg2.Error as e:
        raise e


def update_conversation(
    conversation_id: UUID,
    network_id: int,
    user_id: int,
    title: Optional[str] = None,
    status: Optional[Status] = None,
) -> Conversation:
    db_pool = DatabasePool.get_instance("info_master_genai")
    try:
        with db_pool.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                with conn:
                    where_binding = {
                        "conversation_id": str(conversation_id),
                        "network_id": network_id,
                        "user_id": user_id,
                    }
                    set_binding = {
                        "title": title,
                        "status": status,
                    }

                    where_clause = (
                        build_where_clause(where_binding)
                        + f" AND status != {Status.REMOVE} "
                    )

                    set_clause = (
                        build_set_clause(set_binding) + ", utime = CURRENT_TIMESTAMP"
                    )

                    query = SQL(
                        """
                        UPDATE conversation.conversations
                        SET {set_clause}
                        WHERE {where_clause}
                        RETURNING *
                        """
                    ).format(
                        set_clause=SQL(set_clause),
                        where_clause=SQL(where_clause),
                    )

                    cur.execute(query, {**set_binding, **where_binding})
                    result = cur.fetchone()
                    return result
    except psycopg2.Error as e:
        raise e


def get_conversations(
    network_id: int,
    user_id: int,
    page: int = 1,
    limit: int = 25,
    sort: ConversationColumn = ConversationColumn.UTIME,
    sort_direction: SortDirection = SortDirection.DESC,
    status: Optional[List[Status]] = [Status.ACTIVE],
) -> QueryResult[Conversation]:
    db_pool = DatabasePool.get_instance("info_master_genai")
    try:
        with db_pool.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                with conn:
                    data_binding = {
                        "network_id": network_id,
                        "user_id": user_id,
                        "status": status,
                    }

                    where_clause = build_where_clause(data_binding)

                    query = SQL(
                        """
                        WITH filtered_data AS (
                            SELECT 
                                conversation_id,
                                network_id,
                                user_id,
                                title,
                                model_version,
                                status,
                                ctime,
                                utime
                            FROM conversation.conversations
                            WHERE {where_clause}
                        )
                        SELECT 
                            fd.*,
                            COUNT(*) OVER() as total_count
                        FROM filtered_data fd
                        ORDER BY {sort} {sort_direction}
                        LIMIT {limit}
                        OFFSET {offset}
                        """
                    ).format(
                        where_clause=SQL(where_clause),
                        sort=Identifier(sort.value),
                        sort_direction=SQL(sort_direction.value),
                        limit=SQL(str(limit)),
                        offset=SQL(str((page - 1) * limit)),
                    )

                    cur.execute(query, data_binding)
                    results = cur.fetchall()
                    return get_listing_result(results, Conversation)
    except psycopg2.Error as e:
        raise e


def get_conversation_messages(
    conversation_id: UUID,
    network_id: int,
    user_id: int,
) -> ConversationWithMessages:
    db_pool = DatabasePool.get_instance("info_master_genai")
    try:
        with db_pool.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                with conn:
                    data_binding = {
                        "c.conversation_id": str(conversation_id),
                        "c.network_id": network_id,
                        "c.user_id": user_id,
                        "c.status": Status.ACTIVE,
                    }

                    where_clause = build_where_clause(data_binding)

                    query = SQL(
                        """
                        WITH conversation_data AS (
                            SELECT 
                                c.*,
                                (
                                    SELECT jsonb_agg(m.* ORDER BY m.ctime DESC)
                                    FROM conversation.messages m 
                                    WHERE m.conversation_id = c.conversation_id
                                ) as messages
                            FROM conversation.conversations c
                            WHERE {where_clause}
                        )
                        SELECT 
                            cd.*,
                            jsonb_array_length(cd.messages) as total_messages
                        FROM conversation_data cd
                        """
                    ).format(
                        where_clause=SQL(where_clause),
                    )

                    cur.execute(query, data_binding)
                    results = cur.fetchone()
                    return results
    except psycopg2.Error as e:
        raise e
