from app.common.enums import Status
from app.message.types import Message
from datetime import datetime
from typing import List
from typing_extensions import TypedDict


class Conversation(TypedDict):
    conversation_id: str
    network_id: int
    user_id: int
    title: str
    model_version: str
    status: Status
    ctime: datetime
    utime: datetime


class ConversationWithMessages(Conversation):
    messages: List[Message]
    total_messages: int
