from app.common.schemas import GetListQueryParamsBase
from app.conversation.enums import ConversationColumn
from openai.types.chat import ChatCompletionMessageParam
from pydantic import BaseModel
from typing import Optional, List
from uuid import UUID


class CreateConversationRequest(BaseModel):
    messages: List[ChatCompletionMessageParam]


class UpdateConversationRequest(BaseModel):
    title: Optional[str] = None


class ConversationParams(BaseModel):
    conversation_id: UUID


class GetConversationsQueryParams(GetListQueryParamsBase[ConversationColumn]):
    sort: ConversationColumn
