from app.common.enums import Status, SortDirection
from app.common.types import QueryResult
from app.constants import OPENAI_MODEL
from app.conversation import repositories
from app.conversation.enums import ConversationColumn
from app.conversation.types import Message, Conversation, ConversationWithMessages
from datetime import datetime
from openai.types.chat import ChatCompletionMessageParam
from typing import List, Optional, cast
from uuid import UUID
import json
import logging
import openai


logger = logging.getLogger(__name__)


def get_conversations(
    network_id: int,
    user_id: int,
    page: int,
    limit: int,
    sort: ConversationColumn,
    sort_direction: SortDirection,
) -> QueryResult[Conversation]:
    start_time = datetime.now()
    try:
        logger.info(
            f"""params: {json.dumps({
                'network_id': network_id,
                'user_id': user_id,
                'page': page,
                'limit': limit,
                'sort': sort,
                'sort_direction': sort_direction,
            })}""",
        )

        response = repositories.get_conversations(
            network_id=network_id,
            user_id=user_id,
            page=page,
            limit=limit,
            sort=sort,
            sort_direction=sort_direction,
        )
        logger.info(f"total_count: {response['total_count']}")

        return response
    except Exception as e:
        logger.error(f"error: {str(e)}")
        raise Exception(e)
    finally:
        logger.info(
            f"duration_ms: {(datetime.now() - start_time).total_seconds() * 1000}"
        )


def get_conversation_messages(
    conversation_id: UUID,
    network_id: int,
    user_id: int,
) -> ConversationWithMessages:
    start_time = datetime.now()
    try:
        logger.info(
            f"""params: {json.dumps({
                "conversation_id": str(conversation_id),
                "network_id": network_id,
                "user_id": user_id,
            })}""",
        )

        response = repositories.get_conversation_messages(
            conversation_id=conversation_id,
            network_id=network_id,
            user_id=user_id,
        )
        logger.info(f"response: {response}")

        conversation = {
            key: value
            for key, value in response.items()
            if key not in ["messages", "total_messages", "network_id"]
        }
        logger.info(f"conversation: {conversation}")

        messages: List[Message] = [
            cast(
                Message,
                {
                    key: value
                    for key, value in row.items()
                    if key not in ["token_count"]
                },
            )
            for row in response["messages"]
        ]
        logger.info(f"messages: {messages}")

        return cast(
            ConversationWithMessages,
            {
                **conversation,
                "messages": messages,
                "total_messages": response["total_messages"],
            },
        )
    except Exception as e:
        logger.error(f"error: {str(e)}")
        raise Exception(e)
    finally:
        logger.info(
            f"duration_ms: {(datetime.now() - start_time).total_seconds() * 1000}"
        )


def create_conversation(
    messages: List[ChatCompletionMessageParam],
    network_id: int,
    user_id: int,
) -> Conversation:
    start_time = datetime.now()
    try:
        logger.info(
            f"""params: {json.dumps({
                'network_id': network_id,
                'messages': messages,
                'user_id': user_id
            })}""",
        )

        title = generate_title(messages[0])
        logger.info(f"title: {title}")

        conversation = repositories.create_conversation(
            network_id=network_id,
            user_id=user_id,
            title=title,
            model_version=OPENAI_MODEL,
            status=Status.ACTIVE,
        )
        logger.info(f"conversation: {dict(conversation)}")

        return conversation
    except Exception as e:
        logger.error(f"error: {str(e)}")
        raise Exception(e)
    finally:
        logger.info(
            f"duration_ms: {(datetime.now() - start_time).total_seconds() * 1000}"
        )


def update_conversation(
    conversation_id: UUID,
    network_id: int,
    user_id: int,
    title: Optional[str] = None,
) -> Conversation:
    start_time = datetime.now()
    try:
        logger.info(
            f"""params: {json.dumps({
                "conversation_id": str(conversation_id),
                'network_id':network_id,
                'user_id':user_id,
                'title':title
            })}""",
        )

        conversation = repositories.update_conversation(
            conversation_id=conversation_id,
            network_id=network_id,
            user_id=user_id,
            title=title,
        )
        logger.info(f"conversation: {conversation}")

        return conversation
    except Exception as e:
        logger.error(f"error: {str(e)}")
        raise Exception(e)
    finally:
        logger.info(
            f"duration_ms: {(datetime.now() - start_time).total_seconds() * 1000}"
        )


def delete_conversation(
    conversation_id: UUID,
    network_id: int,
    user_id: int,
) -> bool:
    start_time = datetime.now()
    try:
        logger.info(f"delete_conversation params:: conversation_id:{conversation_id}")

        conversation = repositories.update_conversation(
            conversation_id=conversation_id,
            network_id=network_id,
            user_id=user_id,
            status=Status.REMOVE,
        )
        logger.info(f"conversation: {conversation}")

        return True if conversation else False
    except Exception as e:
        logger.error(f"error: {str(e)}")
        raise Exception(e)
    finally:
        logger.info(
            f"duration_ms: {(datetime.now() - start_time).total_seconds() * 1000}"
        )


def generate_title(
    message: ChatCompletionMessageParam,
) -> str:
    start_time = datetime.now()
    try:
        logger.info(f"params: {json.dumps({'message': message })}")

        system_message: ChatCompletionMessageParam = {
            "role": "system",
            "content": "Your task is to generate a short, descriptive title for the user's input. Do not provide any other response or reply to the user's message.",
        }
        messages: List[ChatCompletionMessageParam] = [system_message, message]

        system_response = openai.chat.completions.create(
            model=OPENAI_MODEL,
            messages=messages,
        )
        logger.info(f"system_response: {system_response}")

        title = system_response.choices[0].message.content
        logger.info(f"title: {title}")

        if not title:
            raise Exception(f"Cannot create title")

        return title
    except Exception as e:
        logger.error(f"error: {str(e)}")
        raise Exception(e)
    finally:
        logger.info(
            f"duration_ms: {(datetime.now() - start_time).total_seconds() * 1000}"
        )
