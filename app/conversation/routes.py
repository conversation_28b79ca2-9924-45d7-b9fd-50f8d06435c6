from app.common.decorators.error_handlers import handle_errors
from app.conversation import business
from app.conversation.schemas import (
    CreateConversationRequest,
    GetConversationsQueryParams,
    UpdateConversationRequest,
)
from app.conversation.schemas import ConversationParams
from app.common.responses import JSONResponse
from app.common.schemas import BaseQueryParams
from authentication import check_permission
from flask import Blueprint, current_app, request
from uuid import UUID
from werkzeug.local import LocalProxy

conversation = Blueprint("conversation", __name__)
logger = LocalProxy(lambda: current_app.logger)


@conversation.before_request
def before_request_func():
    current_app.logger.name = "conversation"


@conversation.route("/", methods=["GET"])
@check_permission
@handle_errors
def get_conversations():
    query = GetConversationsQueryParams(**dict(request.args))  # type: ignore

    response = business.get_conversations(
        network_id=query.network_id,
        user_id=query.user_id,
        page=query.page,
        limit=query.limit,
        sort=query.sort,
        sort_direction=query.sort_direction,
    )

    return JSONResponse(
        code=200,
        message="Success",
        data=response,
    )


@conversation.route("/<conversation_id>/message", methods=["GET"])
@check_permission
@handle_errors
def get_conversation_messages(conversation_id: UUID):
    param = ConversationParams(conversation_id=conversation_id)
    query = BaseQueryParams(**dict(request.args))  # type: ignore

    response = business.get_conversation_messages(
        conversation_id=param.conversation_id,
        network_id=query.network_id,
        user_id=query.user_id,
    )

    return JSONResponse(
        code=200,
        message="Success",
        data=response,
    )


@conversation.route("/", methods=["POST"])
@check_permission
@handle_errors
def create():
    query = BaseQueryParams(**dict(request.args))  # type: ignore
    body = CreateConversationRequest(**dict(request.json))  # type: ignore

    response = business.create_conversation(
        messages=body.messages,
        network_id=query.network_id,
        user_id=query.user_id,
    )

    return JSONResponse(
        code=200,
        message="Success",
        data={
            "conversation_id": response["conversation_id"],
            "title": response["title"],
        },
    )


@conversation.route("/<conversation_id>", methods=["PUT"])
@check_permission
@handle_errors
def update(conversation_id: UUID):
    param = ConversationParams(conversation_id=conversation_id)
    query = BaseQueryParams(**dict(request.args))  # type: ignore
    body = UpdateConversationRequest(**dict(request.json))  # type: ignore

    response = business.update_conversation(
        conversation_id=param.conversation_id,
        network_id=query.network_id,
        user_id=query.user_id,
        title=body.title,
    )

    return JSONResponse(
        code=200,
        message="Success",
        data=response,
    )


@conversation.route("/<conversation_id>", methods=["DELETE"])
@check_permission
@handle_errors
def delete(conversation_id: UUID):
    param = ConversationParams(conversation_id=conversation_id)
    query = BaseQueryParams(**dict(request.args))  # type: ignore

    response = business.delete_conversation(
        conversation_id=param.conversation_id,
        network_id=query.network_id,
        user_id=query.user_id,
    )

    return JSONResponse(
        code=200,
        message="Success",
        data={
            "status": 1 if response is True else 0,
        },
    )
