import redis
from app.library.config import Config
from typing import Dict


client: Dict[str, redis.Redis] = {}


class Redis:
    @staticmethod
    def get_instances(instance) -> redis.Redis | None:
        try:
            config = Config.get("redis")

            instance_info = config.get("redis", {}).get(instance, None)

            if instance and instance_info:
                if instance_info.get("host") and instance_info.get("port"):
                    hostname = instance_info["host"]
                    port = instance_info["port"]

                    if client.get(hostname):
                        return client[hostname]
                    else:
                        client[hostname] = redis.Redis(
                            host=hostname, port=port, decode_responses=True
                        )

                        return client[hostname]
        except Exception as e:
            print("Error: ", e)
            return None
