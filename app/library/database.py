from app.library.config import Config
from contextlib import contextmanager
from psycopg_pool import ConnectionPool
from urllib.parse import quote_plus

import threading
import logging

logger = logging.getLogger(__name__)


class DatabasePool:
    _instances: dict[str, "DatabasePool"] = {}
    _lock = threading.Lock()

    def __init__(self, config: dict):
        """Initialize database pool with config"""
        try:
            encoded_password = quote_plus(config["password"])

            conninfo = (
                f"postgresql://{config['username']}:{encoded_password}@"
                f"{config['host']}:{config['port']}/"
                f"{config['database']}"
            )

            self.pool = ConnectionPool(conninfo, min_size=1, max_size=50)
        except Exception as e:
            logger.error(
                "Failed to create connection pool",
                extra={
                    "path": __file__,
                    "config": {
                        k: v for k, v in config.items() if k != "password"
                    },
                    "error": str(e),
                },
            )
            raise

    @classmethod
    def get_instance(
        cls, instance_name: str = "info_master_genai"
    ) -> "DatabasePool":
        """Get or create pool instance"""
        with cls._lock:
            if (
                instance_name not in cls._instances
                or not cls._instances[instance_name].pool
            ):
                config = Config.get("database")

                if (
                    not config
                    or "postgreSQL" not in config
                    or instance_name not in config["postgreSQL"]
                ):
                    raise ValueError(
                        f"No config found for instance {instance_name}"
                    )

                instance_config = config["postgreSQL"][instance_name]
                required_fields = {
                    "username",
                    "host",
                    "port",
                    "password",
                    "database",
                }

                if not all(
                    field in instance_config for field in required_fields
                ):
                    raise ValueError(
                        f"Missing required config fields for {instance_name}"
                    )

                cls._instances[instance_name] = cls(instance_config)

        return cls._instances[instance_name]

    @contextmanager
    def get_connection(self):
        """Get connection from pool"""
        if not self.pool:
            raise RuntimeError("Pool not initialized")

        conn = self.pool.getconn()
        try:
            yield conn
        finally:
            self.pool.putconn(conn)

    def close(self) -> None:
        """Close pool connections"""
        if self.pool:
            self.pool.close()
            self.pool = None

    @classmethod
    def close_all(cls) -> None:
        """Close all pool instances"""
        with cls._lock:
            for instance in cls._instances.values():
                instance.close()
            cls._instances.clear()
