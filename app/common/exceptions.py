class BaseException(Exception):
    """Base exception for the application"""

    def __init__(self, message: str = ""):
        self.message = message
        super().__init__(self.message)


class UnauthorizedException(BaseException):
    """Raised when user is not authorized"""

    pass


class NotFoundException(BaseException):
    """Raised when resource is not found"""

    pass


class ValidationException(BaseException):
    """Raised when validation fails"""

    pass


class BusinessLogicException(BaseException):
    """Raised when business logic fails"""

    pass


class DatabaseException(BaseException):
    """Raised when database operation fails"""

    pass
