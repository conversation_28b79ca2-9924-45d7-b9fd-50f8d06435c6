from typing import TypeVar, Generic
from app.common.enums import SortDirection
from pydantic import BaseModel, Field


class BaseQueryParams(BaseModel):
    user_id: int
    account_id: int
    network_id: int
    token: str
    app_code: str

    model_config = {"extra": "allow"}


SortType = TypeVar("SortType")


class GetListQueryParamsBase(BaseQueryParams, Generic[SortType]):
    page: int = Field(gt=0)
    limit: int = Field(gt=0, le=500)
    sort: SortType
    sort_direction: SortDirection
