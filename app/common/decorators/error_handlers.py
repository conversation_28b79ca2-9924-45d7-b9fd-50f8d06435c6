from functools import wraps
from typing import Callable
from flask import current_app
from pydantic import ValidationError
from app.common.exceptions import (
    NotFoundException,
    UnauthorizedException,
    BusinessLogicException,
)
from flask import jsonify, Response
from typing import Any


class JSONResponse:
    def __new__(cls, code: int, message: str, data: Any) -> Response:
        return jsonify(
            {
                "code": code,
                "message": message,
                "data": data,
            }
        )


def handle_errors(func: Callable[..., Any]) -> Callable[..., Any]:
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValidationError as e:
            current_app.logger.warning(f"Validation error: {e}", exc_info=True)
            return JSONResponse(code=400, message=str(e), data=None)
        except NotFoundException as e:
            current_app.logger.warning(f"Not found: {e}")
            return JSONResponse(code=404, message=str(e), data=None)
        except UnauthorizedException as e:
            current_app.logger.warning(f"Unauthorized: {e}")
            return JSONResponse(code=401, message=str(e), data=None)
        except BusinessLogicException as e:
            current_app.logger.warning(f"Business logic error: {e}")
            return JSONResponse(code=400, message=str(e), data=None)
        except Exception as e:
            current_app.logger.error(f"Unexpected error: {e}", exc_info=True)
            # return JSONResponse(code=500, message="Internal server error", data=None)
            return JSONResponse(code=500, message=str(e), data=None)

    return wrapper
