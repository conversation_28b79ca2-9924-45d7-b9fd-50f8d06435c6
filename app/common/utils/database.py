from typing import TypeVar, List, Callable, Any, Optional, Dict
from decimal import Decimal
from psycopg2.extras import RealDictRow
from app.common.types import QueryResult
import json

T = TypeVar("T")


def transform_db_row(row: Dict[str, Any]) -> Dict[str, Any]:
    """Transform database row values to appropriate Python types"""

    def transform_value(value: Any) -> Any:
        if isinstance(value, Decimal):
            return int(value)
        return value

    return {k: transform_value(v) for k, v in row.items()}


def get_listing_result(
    results: List[RealDictRow],
    model_class: Optional[Callable[[Any], T]] = None,
) -> QueryResult[T]:
    total_count = results[0]["total_count"] if results else 0

    rows: List[T] = []

    if model_class is None:
        rows = [
            transform_db_row({k: v for k, v in row.items() if k != "total_count"})
            for row in results
        ]  # type: ignore
    else:
        rows = [
            model_class(
                **transform_db_row({k: v for k, v in row.items() if k != "total_count"})  # type: ignore
            )
            for row in results
        ]

    return QueryResult(rows=rows, total_count=total_count)


def build_where_clause(data_binding: dict) -> str:
    """Build WHERE clause with automatic handling of list values using ANY()"""
    return " AND ".join(
        f"{key} = ANY(%({key})s)" if isinstance(value, list) else f"{key} = %({key})s"
        for key, value in data_binding.items()
    )


def build_set_clause(data_binding: dict) -> str:
    """Build SET clause, filtering out None values"""
    clauses = []

    for key, value in data_binding.items():
        if value is None:
            continue
        if isinstance(value, dict):
            data_binding[key] = json.dumps(value)
            clause = f"{key} = COALESCE({key}, '{{}}') || %({key})s::jsonb"
        else:
            clause = f"{key} = %({key})s"

        clauses.append(clause)

    return ", ".join(clauses)


def build_insert_clause(data_binding: dict) -> str:
    """Build INSERT fields and values clause, filtering out None values and converting dicts to JSON"""
    modified_binding = {}

    for key, value in data_binding.items():
        if value is None:
            continue
        if isinstance(value, dict):
            data_binding[key] = json.dumps(value)
            modified_binding[key] = f"%({key})s::jsonb"
        else:
            modified_binding[key] = f"%({key})s"

    fields = ", ".join(modified_binding.keys())
    values = ", ".join(modified_binding.values())

    return f"({fields}) VALUES ({values})"
