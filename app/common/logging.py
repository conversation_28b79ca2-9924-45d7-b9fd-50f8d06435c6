from datetime import datetime
import inspect
import logging
import os


class ContextLogger(logging.Logger):
    def _log(
        self,
        level,
        msg,
        args,
        exc_info=None,
        extra=None,
        stack_info=False,
        stacklevel=1,
    ):
        # Get call stack
        stack = inspect.stack()

        # Find business layer function
        for frame in stack:
            # if "business.py" in frame.filename:
            file_path = os.path.relpath(frame.filename)
            module_name = frame.filename.split("/")[-2]
            function_name = frame.function
            process_id = os.getpid()
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S,%f")[:-3]
            # module_path = f"{module_name}.{function_name}"

            msg = (
                f"{timestamp} - {level} - "
                f"[{process_id}] - "
                f"{module_name} - "
                f"{file_path}:{frame.lineno} - "
                f"{function_name} - "
                f"{msg}"
            )

            break

        super()._log(level, msg, args, exc_info, extra, stack_info, stacklevel)
