from os import environ, path, getcwd
import yaml

constants = {}


class Constant:
    @staticmethod
    def get(key):
        try:
            # Load default constants from file
            default_constants = {}
            default_constants_path = path.join(
                getcwd(),
                "config",
                "domain",
                environ.get('APPLICATION_ENV', 'development'),
                "default.yml",
            )

            if path.exists(default_constants_path):
                with open(default_constants_path, "r") as f:
                    default_constants = yaml.safe_load(f)

            constant = {
                "NETWORK_ID": 10507,
                "JOB_PREFIX_BUYER": "adx_v3:",
                "TOKEN_ROLE": 1,
                "TOKEN_ROLE_ADMIN": 2,
                "TIME_EXPIRE_TOKEN_API": 21600,
                "TYPE_METRIC_REPORT_FILTER": 139,
                "TYPE_METRIC_DATA_SOURCES_FILTER": 206,
                "ERROR_TYPE_FRONT_END": 1,
                "ERROR_TYPE_BACK_END": 2,
                "ERROR_TYPE_BACK_END_PERFORMANCE": 3,
                "JWT_SECRET_KEY": "?gvhQ%qU^@GzZLd3*NGe",
                "KIOT_VIET_API": "https://id.kiotviet.vn",
                "SECURITY_KEY": "aea11f71067d69d337d18ab77809b351",
                "STATIC_PATH": path.join(getcwd(), "static"),
                "CDP_TOKEN": "5474r2x214z254a4u2a4y474641634x544d403d4w5o5",
                "REDIS_KEY_TIMEZONE_CONFIG": "timezone_config",
                "APP_CODE": "APP_ANTALYSER",
                "MENU_CODE": [],
                **default_constants,
                **constants,
            }

            return constant[key] if key else constant
        except Exception as e:
            # Handle error
            print("Error:", e)
            return None
