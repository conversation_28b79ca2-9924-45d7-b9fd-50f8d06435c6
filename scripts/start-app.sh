#!/bin/bash

# Start application script

echo "🚀 Starting Antsomi Generative AI API Application..."

# Check if .env.dev exists
if [ ! -f .env.dev ]; then
  echo "❌ .env.dev file not found!"
  echo "Please create .env.dev file with required environment variables."
  exit 1
fi

# Check if uv is installed
if ! command -v uv &>/dev/null; then
  echo "❌ uv is not installed!"
  echo "Please install uv first: curl -LsSf https://astral.sh/uv/install.sh | sh"
  exit 1
fi

# Load environment variables
export $(grep -v '^#' .env.dev | xargs)

# Check if Redis is running
echo "🔍 Checking Redis connection..."
if ! docker-compose -f docker-compose.dev.yml ps ai-redis | grep -q "Up"; then
  echo "❌ Redis is not running!"
  echo "Please start infrastructure services first: ./scripts/start.sh"
  exit 1
fi

# Check if PostgreSQL is running
echo "🔍 Checking PostgreSQL connection..."
if ! docker-compose -f docker-compose.dev.yml ps ai-postgres | grep -q "Up"; then
  echo "❌ PostgreSQL is not running!"
  echo "Please start infrastructure services first: ./scripts/start.sh"
  exit 1
fi

echo "✅ Infrastructure services are running"
echo "🚀 Starting application on port ${PORT:-5001}..."
echo "📋 Application will be available at: http://localhost:${PORT:-5001}"
echo ""

# Start the application
uv run python run.py
